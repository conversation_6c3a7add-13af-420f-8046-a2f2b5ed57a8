using Multiempresa.Shared.Helpers;
using Multiempresa.Shared.Helpers.Formatadores;
using Multiempresa.Shared.Helpers.ImpressaoRelatorioPdf;
using Multiempresa.Shared.Helpers.ImpressaoRelatorioPdf.Elementos;
using PdfSharpCore.Drawing;
using System.IO;
using System.Text;
using Zendar.Business.Helpers.Formatadores;
using Zendar.Business.ViewModels.Relatorios;
using Zendar.Data.Helpers;

namespace Zendar.Business.Helpers.ImpressaoRelatorioPdf
{
    public sealed class ImpressaoPedidoOrcamentoVenda : Pdf
    {
        private readonly PedidoOrcamentoVendaRelatorioViewModel DadosImpressao;

        public ImpressaoPedidoOrcamentoVenda(PedidoOrcamentoVendaRelatorioViewModel dadosImpressao) : base(15)
        {
            DadosImpressao = dadosImpressao;

            NovaPagina();
            GerarImpressao();
        }


        protected override void AdicionarCabecalho()
        {
            int colunasJaUtilizadas = 0;

            if (DadosImpressao.Loja.LogoRetangularStream != null && DadosImpressao.Loja.LogoRetangularStream.CanRead)
            {
                MemoryStream memoryStream = new();
                DadosImpressao.Loja.LogoRetangularStream.CopyTo(memoryStream);
                memoryStream.Position = 0L;
                DadosImpressao.Loja.LogoRetangularStream.Position = 0L;
                Pincel.CriarImagem(new Imagem(memoryStream, 2.0, 70.0, 0.0, 0) { MargemEsquerda = 20 }, Linha);

                colunasJaUtilizadas = 3;
            }

            Pincel.CriarTexto(new Texto(DadosImpressao.Loja.Fantasia, Pincel.Colunas, Estilos.Fonte(18.0, XFontStyle.Bold), null, null, 11.0), Linha, colunasJaUtilizadas);
            Linha += 23.0;

            Pincel.CriarTexto(new Texto(DadosImpressao.Loja.RazaoSocial, Pincel.Colunas, null, null, null, 11.0), Linha, colunasJaUtilizadas);
            Linha += 12.0;

            Pincel.CriarTexto(new Texto(Formatadores.FormatarEndereco.FormatarEnderecoCompleto(new InformacoesEndereco
            {
                Logradouro = DadosImpressao.Loja.Logradouro,
                Numero = DadosImpressao.Loja.Numero,
                Bairro = DadosImpressao.Loja.Bairro,
                CidadeUf = DadosImpressao.Loja.CidadeUf,
                Cep = DadosImpressao.Loja.Cep
            }), Pincel.Colunas, null, null, null, 11.0), Linha, colunasJaUtilizadas);
            Linha += 12.0;

            var telefoneCelularCpfCnpj = new StringBuilder();

            if (!string.IsNullOrEmpty(DadosImpressao.Loja.Telefone))
                telefoneCelularCpfCnpj.Append(DadosImpressao.Loja.Telefone);

            if (!string.IsNullOrEmpty(DadosImpressao.Loja.Celular))
            {
                if (telefoneCelularCpfCnpj.Length > 0)
                    telefoneCelularCpfCnpj.Append(" / ");

                telefoneCelularCpfCnpj.Append(DadosImpressao.Loja.Celular);
            }

            if (!string.IsNullOrEmpty(DadosImpressao.Loja.CpfCnpj))
            {
                if (!string.IsNullOrEmpty(telefoneCelularCpfCnpj.ToString()))
                    telefoneCelularCpfCnpj.Append(" | ");

                telefoneCelularCpfCnpj.Append(DadosImpressao.Loja.CpfCnpj);
            }

            Pincel.CriarTexto(new Texto(telefoneCelularCpfCnpj.ToString(), Pincel.Colunas, null, null, null, 11.0), Linha, colunasJaUtilizadas);
            Linha += 20.0;

            ObterPosicaoLinha(62);
        }

        protected override void AdicionarRodape()
        {
            CabecalhoRodape.AdicionarRodape(DadosImpressao.Operacao.DataEmissao, this, Site.ObterSite(DadosImpressao.TipoSistema));
        }

        private void GerarImpressao()
        {
            #region [Operacao]
            ObterPosicaoLinha(18);
            Pincel.CriarTexto(new Texto($"{DadosImpressao.Operacao.TipoOperacao.ObterDescricao()}: {DadosImpressao.Operacao.NumeroOperacao}", 9, Estilos.Fonte(14, XFontStyle.Bold), margemEsquerda: 10), Linha);
            Pincel.CriarTexto(new Texto($"Emissão: {DadosImpressao.Operacao.DataEmissao:dd/MM/yyyy HH:mm}", 5, Estilos.Fonte(10), alinhamento: XStringFormats.TopRight), Linha, 10);

            ObterPosicaoLinha(10);
            Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));

            CriarEspacoEmBranco(6);
            #endregion

            #region [Cliente]
            ObterPosicaoLinha(18);
            Pincel.CriarTexto(new Texto("Cliente", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), Linha);

            ObterPosicaoLinha(6);
            Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));

            ObterPosicaoLinha(16);
            var nomeApelido = DadosImpressao.Cliente.Nome;
            if (!string.IsNullOrEmpty(DadosImpressao.Cliente.Apelido))
            {
                nomeApelido += $" / {DadosImpressao.Cliente.Apelido}";
            }

            Pincel.CriarTexto(new Texto($"Nome: {nomeApelido}", 9, Estilos.Fonte(10), margemEsquerda: 10), Linha);
            Pincel.CriarTexto(new Texto($"Saldo em Aberto: R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Cliente.SaldoEmAberto.Value)}", 5, Estilos.Fonte(10), alinhamento: XStringFormats.TopRight), Linha, 10);

            ObterPosicaoLinha(16);
            var cpfCnpjContato = new StringBuilder();
            if (!string.IsNullOrEmpty(DadosImpressao.Cliente.CpfCnpj))
            {
                if (Validacao.ValidarCnpj(DadosImpressao.Cliente.CpfCnpj))
                    cpfCnpjContato.Append($"CNPJ: {DadosImpressao.Cliente.CpfCnpj}");
                else
                    cpfCnpjContato.Append($"CPF: {DadosImpressao.Cliente.CpfCnpj}");

                cpfCnpjContato.Append("          ");
            }

            if (!string.IsNullOrEmpty(DadosImpressao.Cliente.Celular))
            {
                cpfCnpjContato.Append($"Celular: {DadosImpressao.Cliente.Celular}");
                cpfCnpjContato.Append("          ");
            }

            if (!string.IsNullOrEmpty(DadosImpressao.Cliente.Telefone))
            {
                cpfCnpjContato.Append($"Telefone: {DadosImpressao.Cliente.Telefone}");
                cpfCnpjContato.Append("          ");
            }

            if (!string.IsNullOrEmpty(cpfCnpjContato.ToString()))
            {
                Pincel.CriarTexto(new Texto(cpfCnpjContato.ToString(), 9, Estilos.Fonte(10), margemEsquerda: 10), Linha);
            }

            ObterPosicaoLinha(16);
            var endereco = Formatadores.FormatarEndereco.FormatarEnderecoCompleto(new InformacoesEndereco
            {
                Logradouro = DadosImpressao.Cliente.Endereco.Logradouro,
                Numero = DadosImpressao.Cliente.Endereco.Numero,
                Bairro = DadosImpressao.Cliente.Endereco.Bairro,
                CidadeUf = DadosImpressao.Cliente.Endereco.CidadeUf,
                Cep = DadosImpressao.Cliente.Endereco.Cep,
                Complemento = DadosImpressao.Cliente.Endereco.Complemento
            });

            var textoEndereco = new Texto($"Endereço: {endereco}", 15, Estilos.Fonte(10), margemEsquerda: 10);
            var alturaLinhaEndereco = Pincel.CalcularAlturaLinha(textoEndereco, 16);
            Pincel.CriarTextoComQuebraDeLinhas(textoEndereco, Linha, alturaLinhaEndereco);

            CriarEspacoEmBranco(alturaLinhaEndereco);
            #endregion

            #region [Cabecalho Tabela Produtos]
            ObterPosicaoLinha(18);
            Pincel.CriarTexto(new Texto("Itens", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), Linha);

            ObterPosicaoLinha(6);
            Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));

            void AdicionarCabecalhoTabelaProdutos(XUnit position)
            {
                Pincel.CriarRetangulo(Pincel.Retangulo(6, position, Pagina.Width, 16), XBrushes.DarkGray);
                Pincel.CriarTexto(new Texto("Produto", 5, margemEsquerda: 10), position + 2);
                Pincel.CriarTexto(new Texto("Referência", 2, alinhamento: XStringFormats.TopCenter, margemEsquerda: 10), position + 2, 5);
                Pincel.CriarTexto(new Texto("Quantidade", 2, alinhamento: XStringFormats.TopCenter), position + 2, 7);
                Pincel.CriarTexto(new Texto("Valor unit.", 2, alinhamento: XStringFormats.TopRight), position + 2, 9);
                Pincel.CriarTexto(new Texto("Desconto", 2, alinhamento: XStringFormats.TopRight), position + 2, 11);
                Pincel.CriarTexto(new Texto("Valor total", 2, alinhamento: XStringFormats.TopRight, margemEsquerda: 10), position + 2, 13);
            }

            ObterPosicaoLinha(16);

            AdicionarCabecalhoTabelaProdutos(Linha);
            #endregion

            #region [Listagem Tabela Produtos]
            var c = 0; // Contador, utilizado para deifinir a cor da linha
            foreach (var item in DadosImpressao.OperacaoItens)
            {
                // Seleciona uma cor para pintar as linhas alternadamente
                XSolidBrush cor = Pincel.AlternarCorDaLinha(c);

                var descricao = new Texto(item.Descricao.ToTitle(), 5, margemEsquerda: 10);

                //Calcula o tamanho da linha, para caso haja quebra de linha
                var alturaLinha = Pincel.CalcularAlturaLinha(descricao, 16);

                var paginaAnterior = Doc.PageCount;
                ObterPosicaoLinha(alturaLinha);

                // Se houve quebra de página, imprime o cabeçalho da listagem novamente
                if (Doc.PageCount > paginaAnterior)
                {
                    ObterPosicaoLinha(16);
                    AdicionarCabecalhoTabelaProdutos(Linha);
                    ObterPosicaoLinha(alturaLinha);
                }

                Pincel.CriarRetangulo(Pincel.Retangulo(6, Linha, Pagina.Width, alturaLinha), cor);
                Pincel.CriarTextoComQuebraDeLinhas(descricao, Linha + 2, 16);
                Pincel.CriarTexto(new Texto(item.Referencia ?? "Sem referência", 2, alinhamento: XStringFormats.TopCenter, margemEsquerda: 10), Linha + 2, 5);
                Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(item.Quantidade), 2, alinhamento: XStringFormats.TopCenter), Linha + 2, 7);
                Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(item.ValorUnitario), 2, alinhamento: XStringFormats.TopRight), Linha + 2, 9);
                Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(item.ValorDescontoItem), 2, alinhamento: XStringFormats.TopRight), Linha + 2, 11);
                Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(item.ValorItemComDesconto), 2, alinhamento: XStringFormats.TopRight, margemEsquerda: 10), Linha + 2, 13);

                c++; // Contador
            }
            ObterPosicaoLinha(12);
            Pincel.CriarLinha(Pincel.Retangulo(6, Linha + 5, Pagina.Width, Linha + 5));           
            #endregion

            #region [Totalizadores]
            int primeiraColuna = 12;
            int segundaColuna = 3;
            var paginaAnteriorTotalizadores = Doc.PageCount;

            void AdicionarCabecalhoTotalizadores(XUnit position)
            {
                Pincel.CriarTexto(new Texto("Totalizadores", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), position);
                ObterPosicaoLinha(6);
                Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));
                ObterPosicaoLinha(16);
            }

            ObterPosicaoLinha();

            if (Doc.PageCount > paginaAnteriorTotalizadores)
            {
                ObterPosicaoLinha();
                AdicionarCabecalhoTotalizadores(Linha);
            }

            var paginaAnteriorTotalItens = Doc.PageCount;

            if (Doc.PageCount > paginaAnteriorTotalItens)
            {
                ObterPosicaoLinha();
                AdicionarCabecalhoTotalizadores(Linha);
            }
            Pincel.CriarTexto(new Texto("Total de itens:", primeiraColuna, Estilos.Fonte(10.0), null, XStringFormats.CenterLeft, 5.0), Linha);
            Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.TotalIens), segundaColuna, Estilos.Fonte(10.0), null, XStringFormats.TopLeft, 5.0), Linha, 2);

            var paginaAnteriorValorProdutos = Doc.PageCount;
            if (Doc.PageCount > paginaAnteriorValorProdutos)
            {
                ObterPosicaoLinha();
                AdicionarCabecalhoTotalizadores(Linha);
            }
            Pincel.CriarTexto(new Texto("Valor dos produtos:", primeiraColuna, Estilos.Fonte(10.0), null, XStringFormats.CenterRight, 5.0), Linha);
            Pincel.CriarTexto(new Texto($"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.ValorTotalProdutos)}", segundaColuna, Estilos.Fonte(10.0), null, XStringFormats.TopRight, 5.0), Linha, primeiraColuna);

            var paginaAnteriorAcrescimos = Doc.PageCount;
            ObterPosicaoLinha();
            if (Doc.PageCount > paginaAnteriorAcrescimos)
            {
                ObterPosicaoLinha();
                AdicionarCabecalhoTotalizadores(Linha);
            }
            Pincel.CriarTexto(new Texto("Acréscimos:", primeiraColuna, Estilos.Fonte(10.0), null, XStringFormats.CenterRight, 5.0), Linha);
            Pincel.CriarTexto(new Texto($"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.Acrescimos)}", segundaColuna, Estilos.Fonte(10.0), null, XStringFormats.TopRight, 5.0), Linha, primeiraColuna);

            if (DadosImpressao.Operacao.ValorTotalFrete > 0)
            {
                var paginaAnteriorFrete = Doc.PageCount;
                ObterPosicaoLinha();
                if (Doc.PageCount > paginaAnteriorFrete)
                {
                    ObterPosicaoLinha(16);
                    AdicionarCabecalhoTotalizadores(Linha);
                }
                Pincel.CriarTexto(new Texto("Frete:", primeiraColuna, Estilos.Fonte(10.0), null, XStringFormats.CenterRight, 5.0), Linha);
                Pincel.CriarTexto(new Texto($"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.ValorTotalFrete)}", segundaColuna, Estilos.Fonte(10.0), null, XStringFormats.TopRight, 5.0), Linha, primeiraColuna);
            }

            var paginaAnteriorDescontos = Doc.PageCount;
            ObterPosicaoLinha();
            if (Doc.PageCount > paginaAnteriorDescontos)
            {
                ObterPosicaoLinha();
                AdicionarCabecalhoTotalizadores(Linha);
            }
            Pincel.CriarTexto(new Texto("Descontos:", primeiraColuna, Estilos.Fonte(10.0), null, XStringFormats.CenterRight, 5.0), Linha);
            Pincel.CriarTexto(new Texto($"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.Descontos)}", segundaColuna, Estilos.Fonte(10.0), null, XStringFormats.TopRight, 5.0), Linha, primeiraColuna);

            var paginaAnteriorTotal = Doc.PageCount;
            ObterPosicaoLinha();
            if (Doc.PageCount > paginaAnteriorTotal)
            {
                ObterPosicaoLinha(16);
                AdicionarCabecalhoTotalizadores(Linha);
            }
            Pincel.CriarTexto(new Texto("TOTAL:", primeiraColuna, Estilos.Fonte(10.0, XFontStyle.Bold), null, XStringFormats.CenterRight, 5.0), Linha);
            Pincel.CriarTexto(new Texto($"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.ValorTotal)}", segundaColuna, Estilos.Fonte(10.0, XFontStyle.Bold), null, XStringFormats.TopRight, 5.0), Linha, primeiraColuna);

            if (DadosImpressao.Operacao.Troco > 0)
            {
                var paginaAnteriorTroco = Doc.PageCount;
                ObterPosicaoLinha();
                if (Doc.PageCount > paginaAnteriorTroco)
                {
                    ObterPosicaoLinha();
                    AdicionarCabecalhoTotalizadores(Linha);
                }
                Pincel.CriarTexto(new Texto("Troco:", primeiraColuna, Estilos.Fonte(10.0), null, XStringFormats.CenterRight, 5.0), Linha);
                Pincel.CriarTexto(new Texto($"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.Troco)}", segundaColuna, Estilos.Fonte(10.0), null, XStringFormats.TopRight, 5.0), Linha, primeiraColuna);
            }
            #endregion

            #region [Pagamento]
            if (DadosImpressao.MovimentacoesFinanceiras.Count > 0)
            {
                var paginaAnteriorPagamentos = Doc.PageCount;

                void AdicionarCabecalhoPagamentos(XUnit position)
                {
                    ObterPosicaoLinha(18);
                    Pincel.CriarTexto(new Texto("Pagamento", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), position);
            
                    ObterPosicaoLinha(6);
                    Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));
                }

                if (Doc.PageCount > paginaAnteriorPagamentos)
                {
                    ObterPosicaoLinha(16);
                    AdicionarCabecalhoPagamentos(Linha);
                }
                else
                {
                    ObterPosicaoLinha(18);
                    AdicionarCabecalhoPagamentos(Linha);
                }

                #region [Cabecalho Tabela Movimentacao Financeira]
                void CabecalhoMovimentacaoFinanceira(XUnit position)
                {
                    Pincel.CriarRetangulo(Pincel.Retangulo(6, position, Pagina.Width, 16), XBrushes.DarkGray);
                    Pincel.CriarTexto(new Texto("Parcela", 2, margemEsquerda: 10), position + 2);
                    Pincel.CriarTexto(new Texto("Vencimento", 3, alinhamento: XStringFormats.TopCenter), position + 2, 2);
                    Pincel.CriarTexto(new Texto("Valor", 3, alinhamento: XStringFormats.TopRight), position + 2, 4);
                    Pincel.CriarTexto(new Texto("Forma de recebimento", 7, margemEsquerda: 20), position + 2, 8);
                }

                ObterPosicaoLinha(16);

                CabecalhoMovimentacaoFinanceira(Linha);
                #endregion

                #region [Listagem Tabela Movimentacoes Financeiras]
                foreach (var movimentacao in DadosImpressao.MovimentacoesFinanceiras)
                {
                    var paginaAnterior = Doc.PageCount;

                    ObterPosicaoLinha(16);

                    // Se houve quebra de página, imprime o cabeçalho da listagem novamente
                    if (Doc.PageCount > paginaAnterior)
                    {
                        ObterPosicaoLinha(16);
                        AdicionarCabecalhoPagamentos(Linha);
                        ObterPosicaoLinha(16);
                        CabecalhoMovimentacaoFinanceira(Linha);
                        ObterPosicaoLinha(16);
                    }

                    Pincel.CriarTexto(new Texto(movimentacao.NumeroParcela, 2, margemEsquerda: 10), Linha);
                    Pincel.CriarTexto(new Texto(movimentacao.DataVencimento.ToString("dd/MM/yyyy"), 3, alinhamento: XStringFormats.TopCenter), Linha, 2);
                    Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(movimentacao.Valor), 3, alinhamento: XStringFormats.TopRight), Linha, 4);
                    Pincel.CriarTexto(new Texto(movimentacao.GetDescricaoPagamento(), 7, margemEsquerda: 20), Linha, 8);
                }
                #endregion
            }
            #endregion

            #region [Dados adicionais]

            var paginaAnteriorDadosAdicionais = Doc.PageCount;
            // Se houve quebra de página, recriar o cabeçalho dos dados adicionais
            if (Doc.PageCount <= paginaAnteriorDadosAdicionais)
            {
                 CriarEspacoEmBranco(30);
            }

            ObterPosicaoLinha(18);
            Pincel.CriarTexto(new Texto("Dados adicionais", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), Linha);

            ObterPosicaoLinha(6);
            Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));

            // Vendedor - verificar quebra de página
            var paginaAnteriorVendedor = Doc.PageCount;
            ObterPosicaoLinha(16);
            if (Doc.PageCount > paginaAnteriorVendedor)
            {
                ObterPosicaoLinha(16);
                Pincel.CriarTexto(new Texto("Dados adicionais", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), Linha);

                ObterPosicaoLinha(6);
                Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));
            }
            Pincel.CriarTexto(new Texto($"Vendedor(a): {DadosImpressao.Operacao.Vendedor}", 9, Estilos.Fonte(10), margemEsquerda: 10), Linha);
                
            // Operador - verificar quebra de página
            var paginaAnteriorOperador = Doc.PageCount;
            ObterPosicaoLinha(16);
            if (Doc.PageCount > paginaAnteriorOperador)
            {
                ObterPosicaoLinha(16);
                Pincel.CriarTexto(new Texto("Dados adicionais", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), Linha);

                ObterPosicaoLinha(6);
                Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));
            }
            ObterPosicaoLinha(16);
            Pincel.CriarTexto(new Texto($"Operador(a): {DadosImpressao.Operacao.Usuario}", 9, Estilos.Fonte(10), margemEsquerda: 10), Linha);

            if (!string.IsNullOrEmpty(DadosImpressao.Operacao.Observacao))
            {
                var textoObservacao = new Texto($"Observação: {DadosImpressao.Operacao.Observacao}", 15, Estilos.Fonte(10), margemEsquerda: 10);
                var alturaLinha = Pincel.CalcularAlturaLinha(textoObservacao, 16);

                var paginaAnterior = Doc.PageCount;
                ObterPosicaoLinha(alturaLinha);

                // Se houve quebra de página
                if (Doc.PageCount > paginaAnterior)
                {
                    ObterPosicaoLinha(16);
                    Pincel.CriarTexto(new Texto("Dados adicionais", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), Linha);

                    ObterPosicaoLinha(6);
                    Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));
                }
                Pincel.CriarTextoComQuebraDeLinhas(textoObservacao, Linha, Pincel.CalcularAlturaLinha(textoObservacao, 16));
            }
            #endregion
        }
    }
}