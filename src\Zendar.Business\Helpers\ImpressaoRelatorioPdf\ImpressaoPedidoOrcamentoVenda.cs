using Multiempresa.Shared.Helpers;
using Multiempresa.Shared.Helpers.Formatadores;
using Multiempresa.Shared.Helpers.ImpressaoRelatorioPdf;
using Multiempresa.Shared.Helpers.ImpressaoRelatorioPdf.Elementos;
using PdfSharpCore.Drawing;
using System;
using System.IO;
using System.Text;
using Zendar.Business.Helpers.Formatadores;
using Zendar.Business.ViewModels.Relatorios;
using Zendar.Data.Helpers;

namespace Zendar.Business.Helpers.ImpressaoRelatorioPdf
{
    public sealed class ImpressaoPedidoOrcamentoVenda : Pdf
    {
        private readonly PedidoOrcamentoVendaRelatorioViewModel DadosImpressao;

        public ImpressaoPedidoOrcamentoVenda(PedidoOrcamentoVendaRelatorioViewModel dadosImpressao) : base(15)
        {
            DadosImpressao = dadosImpressao;

            NovaPagina();
            GerarImpressao();
        }

        /// <summary>
        /// Executa uma ação verificando se houve quebra de página e executando um callback se necessário
        /// </summary>
        /// <param name="acao">Ação principal a ser executada</param>
        /// <param name="callbackQuebraPagina">Callback executado se houver quebra de página</param>
        private void ExecutarComVerificacaoQuebraPagina(Action acao, Action callbackQuebraPagina = null)
        {
            var paginaAnterior = Doc.PageCount;
            acao();

            if (Doc.PageCount > paginaAnterior && callbackQuebraPagina != null)
            {
                callbackQuebraPagina();
            }
        }

        /// <summary>
        /// Obtém posição da linha verificando quebra de página e executando callback se necessário
        /// </summary>
        /// <param name="altura">Altura da linha</param>
        /// <param name="callbackQuebraPagina">Callback executado se houver quebra de página</param>
        private void ObterPosicaoLinhaComVerificacao(double altura, Action callbackQuebraPagina = null)
        {
            ExecutarComVerificacaoQuebraPagina(() => ObterPosicaoLinha(altura), callbackQuebraPagina);
        }

        /// <summary>
        /// Sobrecarga sem altura específica
        /// </summary>
        private void ObterPosicaoLinhaComVerificacao(Action callbackQuebraPagina = null)
        {
            ExecutarComVerificacaoQuebraPagina(() => ObterPosicaoLinha(), callbackQuebraPagina);
        }


        protected override void AdicionarCabecalho()
        {
            int colunasJaUtilizadas = 0;

            if (DadosImpressao.Loja.LogoRetangularStream != null && DadosImpressao.Loja.LogoRetangularStream.CanRead)
            {
                MemoryStream memoryStream = new();
                DadosImpressao.Loja.LogoRetangularStream.CopyTo(memoryStream);
                memoryStream.Position = 0L;
                DadosImpressao.Loja.LogoRetangularStream.Position = 0L;
                Pincel.CriarImagem(new Imagem(memoryStream, 2.0, 70.0, 0.0, 0) { MargemEsquerda = 20 }, Linha);

                colunasJaUtilizadas = 3;
            }

            Pincel.CriarTexto(new Texto(DadosImpressao.Loja.Fantasia, Pincel.Colunas, Estilos.Fonte(18.0, XFontStyle.Bold), null, null, 11.0), Linha, colunasJaUtilizadas);
            Linha += 23.0;

            Pincel.CriarTexto(new Texto(DadosImpressao.Loja.RazaoSocial, Pincel.Colunas, null, null, null, 11.0), Linha, colunasJaUtilizadas);
            Linha += 12.0;

            Pincel.CriarTexto(new Texto(Formatadores.FormatarEndereco.FormatarEnderecoCompleto(new InformacoesEndereco
            {
                Logradouro = DadosImpressao.Loja.Logradouro,
                Numero = DadosImpressao.Loja.Numero,
                Bairro = DadosImpressao.Loja.Bairro,
                CidadeUf = DadosImpressao.Loja.CidadeUf,
                Cep = DadosImpressao.Loja.Cep
            }), Pincel.Colunas, null, null, null, 11.0), Linha, colunasJaUtilizadas);
            Linha += 12.0;

            var telefoneCelularCpfCnpj = new StringBuilder();

            if (!string.IsNullOrEmpty(DadosImpressao.Loja.Telefone))
                telefoneCelularCpfCnpj.Append(DadosImpressao.Loja.Telefone);

            if (!string.IsNullOrEmpty(DadosImpressao.Loja.Celular))
            {
                if (telefoneCelularCpfCnpj.Length > 0)
                    telefoneCelularCpfCnpj.Append(" / ");

                telefoneCelularCpfCnpj.Append(DadosImpressao.Loja.Celular);
            }

            if (!string.IsNullOrEmpty(DadosImpressao.Loja.CpfCnpj))
            {
                if (!string.IsNullOrEmpty(telefoneCelularCpfCnpj.ToString()))
                    telefoneCelularCpfCnpj.Append(" | ");

                telefoneCelularCpfCnpj.Append(DadosImpressao.Loja.CpfCnpj);
            }

            Pincel.CriarTexto(new Texto(telefoneCelularCpfCnpj.ToString(), Pincel.Colunas, null, null, null, 11.0), Linha, colunasJaUtilizadas);
            Linha += 20.0;

            ObterPosicaoLinha(62);
        }

        protected override void AdicionarRodape()
        {
            CabecalhoRodape.AdicionarRodape(DadosImpressao.Operacao.DataEmissao, this, Site.ObterSite(DadosImpressao.TipoSistema));
        }

        private void GerarImpressao()
        {
            #region [Operacao]
            ObterPosicaoLinha(18);
            Pincel.CriarTexto(new Texto($"{DadosImpressao.Operacao.TipoOperacao.ObterDescricao()}: {DadosImpressao.Operacao.NumeroOperacao}", 9, Estilos.Fonte(14, XFontStyle.Bold), margemEsquerda: 10), Linha);
            Pincel.CriarTexto(new Texto($"Emissão: {DadosImpressao.Operacao.DataEmissao:dd/MM/yyyy HH:mm}", 5, Estilos.Fonte(10), alinhamento: XStringFormats.TopRight), Linha, 10);

            ObterPosicaoLinha(10);
            Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));

            CriarEspacoEmBranco(6);
            #endregion

            #region [Cliente]
            ObterPosicaoLinha(18);
            Pincel.CriarTexto(new Texto("Cliente", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), Linha);

            ObterPosicaoLinha(6);
            Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));

            ObterPosicaoLinha(16);
            var nomeApelido = DadosImpressao.Cliente.Nome;
            if (!string.IsNullOrEmpty(DadosImpressao.Cliente.Apelido))
            {
                nomeApelido += $" / {DadosImpressao.Cliente.Apelido}";
            }

            Pincel.CriarTexto(new Texto($"Nome: {nomeApelido}", 9, Estilos.Fonte(10), margemEsquerda: 10), Linha);
            Pincel.CriarTexto(new Texto($"Saldo em Aberto: R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Cliente.SaldoEmAberto.Value)}", 5, Estilos.Fonte(10), alinhamento: XStringFormats.TopRight), Linha, 10);

            ObterPosicaoLinha(16);
            var cpfCnpjContato = new StringBuilder();
            if (!string.IsNullOrEmpty(DadosImpressao.Cliente.CpfCnpj))
            {
                if (Validacao.ValidarCnpj(DadosImpressao.Cliente.CpfCnpj))
                    cpfCnpjContato.Append($"CNPJ: {DadosImpressao.Cliente.CpfCnpj}");
                else
                    cpfCnpjContato.Append($"CPF: {DadosImpressao.Cliente.CpfCnpj}");

                cpfCnpjContato.Append("          ");
            }

            if (!string.IsNullOrEmpty(DadosImpressao.Cliente.Celular))
            {
                cpfCnpjContato.Append($"Celular: {DadosImpressao.Cliente.Celular}");
                cpfCnpjContato.Append("          ");
            }

            if (!string.IsNullOrEmpty(DadosImpressao.Cliente.Telefone))
            {
                cpfCnpjContato.Append($"Telefone: {DadosImpressao.Cliente.Telefone}");
                cpfCnpjContato.Append("          ");
            }

            if (!string.IsNullOrEmpty(cpfCnpjContato.ToString()))
            {
                Pincel.CriarTexto(new Texto(cpfCnpjContato.ToString(), 9, Estilos.Fonte(10), margemEsquerda: 10), Linha);
            }

            ObterPosicaoLinha(16);
            var endereco = Formatadores.FormatarEndereco.FormatarEnderecoCompleto(new InformacoesEndereco
            {
                Logradouro = DadosImpressao.Cliente.Endereco.Logradouro,
                Numero = DadosImpressao.Cliente.Endereco.Numero,
                Bairro = DadosImpressao.Cliente.Endereco.Bairro,
                CidadeUf = DadosImpressao.Cliente.Endereco.CidadeUf,
                Cep = DadosImpressao.Cliente.Endereco.Cep,
                Complemento = DadosImpressao.Cliente.Endereco.Complemento
            });

            var textoEndereco = new Texto($"Endereço: {endereco}", 15, Estilos.Fonte(10), margemEsquerda: 10);
            var alturaLinhaEndereco = Pincel.CalcularAlturaLinha(textoEndereco, 16);
            Pincel.CriarTextoComQuebraDeLinhas(textoEndereco, Linha, alturaLinhaEndereco);

            CriarEspacoEmBranco(alturaLinhaEndereco);
            #endregion

            #region [Cabecalho Tabela Produtos]
            ObterPosicaoLinha(18);
            Pincel.CriarTexto(new Texto("Itens", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), Linha);

            ObterPosicaoLinha(6);
            Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));

            void AdicionarCabecalhoTabelaProdutos(XUnit position)
            {
                Pincel.CriarRetangulo(Pincel.Retangulo(6, position, Pagina.Width, 16), XBrushes.DarkGray);
                Pincel.CriarTexto(new Texto("Produto", 5, margemEsquerda: 10), position + 2);
                Pincel.CriarTexto(new Texto("Referência", 2, alinhamento: XStringFormats.TopCenter, margemEsquerda: 10), position + 2, 5);
                Pincel.CriarTexto(new Texto("Quantidade", 2, alinhamento: XStringFormats.TopCenter), position + 2, 7);
                Pincel.CriarTexto(new Texto("Valor unit.", 2, alinhamento: XStringFormats.TopRight), position + 2, 9);
                Pincel.CriarTexto(new Texto("Desconto", 2, alinhamento: XStringFormats.TopRight), position + 2, 11);
                Pincel.CriarTexto(new Texto("Valor total", 2, alinhamento: XStringFormats.TopRight, margemEsquerda: 10), position + 2, 13);
            }

            ObterPosicaoLinha(16);

            AdicionarCabecalhoTabelaProdutos(Linha);
            #endregion

            #region [Listagem Tabela Produtos]
            var c = 0; // Contador, utilizado para deifinir a cor da linha
            foreach (var item in DadosImpressao.OperacaoItens)
            {
                // Seleciona uma cor para pintar as linhas alternadamente
                XSolidBrush cor = Pincel.AlternarCorDaLinha(c);

                var descricao = new Texto(item.Descricao.ToTitle(), 5, margemEsquerda: 10);

                //Calcula o tamanho da linha, para caso haja quebra de linha
                var alturaLinha = Pincel.CalcularAlturaLinha(descricao, 16);

                // Verifica quebra de página e reimprime cabeçalho se necessário
                ObterPosicaoLinhaComVerificacao(alturaLinha, () =>
                {
                    ObterPosicaoLinha(16);
                    AdicionarCabecalhoTabelaProdutos(Linha);
                    ObterPosicaoLinha(alturaLinha);
                });

                Pincel.CriarRetangulo(Pincel.Retangulo(6, Linha, Pagina.Width, alturaLinha), cor);
                Pincel.CriarTextoComQuebraDeLinhas(descricao, Linha + 2, 16);
                Pincel.CriarTexto(new Texto(item.Referencia ?? "Sem referência", 2, alinhamento: XStringFormats.TopCenter, margemEsquerda: 10), Linha + 2, 5);
                Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(item.Quantidade), 2, alinhamento: XStringFormats.TopCenter), Linha + 2, 7);
                Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(item.ValorUnitario), 2, alinhamento: XStringFormats.TopRight), Linha + 2, 9);
                Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(item.ValorDescontoItem), 2, alinhamento: XStringFormats.TopRight), Linha + 2, 11);
                Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(item.ValorItemComDesconto), 2, alinhamento: XStringFormats.TopRight, margemEsquerda: 10), Linha + 2, 13);

                c++; // Contador
            }
            ObterPosicaoLinha(12);
            Pincel.CriarLinha(Pincel.Retangulo(6, Linha + 5, Pagina.Width, Linha + 5));
            #endregion

            #region [Totalizadores]
            int primeiraColuna = 12;
            int segundaColuna = 3;

            void AdicionarCabecalhoTotalizadores(XUnit position)
            {
                Pincel.CriarTexto(new Texto("Totalizadores", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), position);
                ObterPosicaoLinha(6);
                Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));
                ObterPosicaoLinha(16);
            }

            void CriarItemTotalizador(string label, string valor, bool negrito = false)
            {
                ObterPosicaoLinhaComVerificacao(() => AdicionarCabecalhoTotalizadores(Linha));

                var fonte = negrito ? Estilos.Fonte(10.0, XFontStyle.Bold) : Estilos.Fonte(10.0);
                Pincel.CriarTexto(new Texto(label, primeiraColuna, fonte, null, XStringFormats.CenterRight, 5.0), Linha);
                Pincel.CriarTexto(new Texto(valor, segundaColuna, fonte, null, XStringFormats.TopRight, 5.0), Linha, primeiraColuna);
            }

            //ObterPosicaoLinha();

            // Total de itens
            CriarItemTotalizador("Total de itens:", FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.TotalIens));

            // Valor dos produtos
            CriarItemTotalizador("Valor dos produtos:", $"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.ValorTotalProdutos)}");

            // Acréscimos
            CriarItemTotalizador("Acréscimos:", $"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.Acrescimos)}");

            // Frete (se houver)
            if (DadosImpressao.Operacao.ValorTotalFrete > 0)
            {
                CriarItemTotalizador("Frete:", $"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.ValorTotalFrete)}");
            }

            // Descontos
            CriarItemTotalizador("Descontos:", $"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.Descontos)}");

            // Total (em negrito)
            CriarItemTotalizador("TOTAL:", $"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.ValorTotal)}", true);

            // Troco (se houver)
            if (DadosImpressao.Operacao.Troco > 0)
            {
                CriarItemTotalizador("Troco:", $"R$ {FormatarValor.FormatarValorComPontuacao(DadosImpressao.Operacao.Troco)}");
            }
            #endregion

            #region [Pagamento]
            if (DadosImpressao.MovimentacoesFinanceiras.Count > 0)
            {
                void AdicionarCabecalhoPagamentos(XUnit position)
                {
                    Pincel.CriarTexto(new Texto("Pagamento", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), position);

                    ObterPosicaoLinha(6);
                    Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));
                }

                void CabecalhoMovimentacaoFinanceira(XUnit position)
                {
                    Pincel.CriarRetangulo(Pincel.Retangulo(6, position, Pagina.Width, 16), XBrushes.DarkGray);
                    Pincel.CriarTexto(new Texto("Parcela", 2, margemEsquerda: 10), position + 2);
                    Pincel.CriarTexto(new Texto("Vencimento", 3, alinhamento: XStringFormats.TopCenter), position + 2, 2);
                    Pincel.CriarTexto(new Texto("Valor", 3, alinhamento: XStringFormats.TopRight), position + 2, 4);
                    Pincel.CriarTexto(new Texto("Forma de recebimento", 7, margemEsquerda: 20), position + 2, 8);
                }

                // Adicionar cabeçalho inicial
                ObterPosicaoLinha(18);
                AdicionarCabecalhoPagamentos(Linha);

                // Adicionar cabeçalho da tabela
                ObterPosicaoLinha(16);
                CabecalhoMovimentacaoFinanceira(Linha);

                #region [Listagem Tabela Movimentacoes Financeiras]
                foreach (var movimentacao in DadosImpressao.MovimentacoesFinanceiras)
                {
                    // Verifica quebra de página e reimprime cabeçalhos se necessário
                    ObterPosicaoLinhaComVerificacao(16, () =>
                    {
                        ObterPosicaoLinha(16);
                        AdicionarCabecalhoPagamentos(Linha);
                        ObterPosicaoLinha(16);
                        CabecalhoMovimentacaoFinanceira(Linha);
                        ObterPosicaoLinha(16);
                    });

                    Pincel.CriarTexto(new Texto(movimentacao.NumeroParcela, 2, margemEsquerda: 10), Linha);
                    Pincel.CriarTexto(new Texto(movimentacao.DataVencimento.ToString("dd/MM/yyyy"), 3, alinhamento: XStringFormats.TopCenter), Linha, 2);
                    Pincel.CriarTexto(new Texto(FormatarValor.FormatarValorComPontuacao(movimentacao.Valor), 3, alinhamento: XStringFormats.TopRight), Linha, 4);
                    Pincel.CriarTexto(new Texto(movimentacao.GetDescricaoPagamento(), 7, margemEsquerda: 20), Linha, 8);
                }
                #endregion
            }
            #endregion

            #region [Dados adicionais]

            void AdicionarCabecalhoDadosAdicionais()
            {
                Pincel.CriarTexto(new Texto("Dados adicionais", 8, Estilos.Fonte(12, XFontStyle.Bold), margemEsquerda: 10), Linha);
                ObterPosicaoLinha(6);
                Pincel.CriarLinha(Pincel.Retangulo(6, Linha, Pagina.Width, Linha));
            }

            // Espaço inicial e cabeçalho
            CriarEspacoEmBranco(30);
            ObterPosicaoLinha(18);
            AdicionarCabecalhoDadosAdicionais();

            // Vendedor - verificar quebra de página
            ObterPosicaoLinhaComVerificacao(16, () =>
            {
                ObterPosicaoLinha(16);
                AdicionarCabecalhoDadosAdicionais();
            });
            Pincel.CriarTexto(new Texto($"Vendedor(a): {DadosImpressao.Operacao.Vendedor}", 9, Estilos.Fonte(10), margemEsquerda: 10), Linha);

            // Operador - verificar quebra de página
            ObterPosicaoLinhaComVerificacao(16, () =>
            {
                ObterPosicaoLinha(16);
                AdicionarCabecalhoDadosAdicionais();
            });
            Pincel.CriarTexto(new Texto($"Operador(a): {DadosImpressao.Operacao.Usuario}", 9, Estilos.Fonte(10), margemEsquerda: 10), Linha);

            // Observação (se houver)
            if (!string.IsNullOrEmpty(DadosImpressao.Operacao.Observacao))
            {
                var textoObservacao = new Texto($"Observação: {DadosImpressao.Operacao.Observacao}", 15, Estilos.Fonte(10), margemEsquerda: 10);
                var alturaLinha = Pincel.CalcularAlturaLinha(textoObservacao, 16);

                // Verificar quebra de página para observação
                ObterPosicaoLinhaComVerificacao(alturaLinha, () =>
                {
                    ObterPosicaoLinha(16);
                    AdicionarCabecalhoDadosAdicionais();
                });

                Pincel.CriarTextoComQuebraDeLinhas(textoObservacao, Linha, alturaLinha);
            }
            #endregion
        }
    }
}